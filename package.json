{"name": "starter", "version": "0.0.0", "license": "MIT", "updateBuild": "node ./replace.build.js", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "updateBuild": "node ./replace.build.js"}, "private": true, "dependencies": {"@agm/core": "1.0.0-beta.2", "@angular/animations": "4.4.3", "@angular/cdk": "2.0.0-beta.11", "@angular/common": "4.4.3", "@angular/compiler": "4.4.3", "@angular/core": "4.4.3", "@angular/flex-layout": "2.0.0-beta.9", "@angular/forms": "4.4.3", "@angular/http": "4.4.3", "@angular/material": "2.0.0-beta.11", "@angular/platform-browser": "4.4.3", "@angular/platform-browser-dynamic": "4.4.3", "@angular/platform-server": "4.4.3", "@angular/router": "4.4.3", "@ngx-translate/core": "8.0.0", "@types/file-saver": "^1.3.0", "@types/jest": "^24.0.11", "@types/mocha": "^5.2.6", "angular-calendar": "0.22.3", "angular-font-awesome": "^3.1.2", "angular-tree-component": "7.0.2-beta1", "angular2-prettyjson": "2.0.6", "chart.js": "2.6.0", "core-js": "2.5.1", "file-saver": "^1.3.3", "font-awesome": "^4.7.0", "hammerjs": "2.0.8", "md2": "0.0.29", "moment": "2.18.1", "ng2-ace-editor": "0.3.1", "ng2-charts": "1.6.0", "ngx-editor": "^1.2.1", "ng2-nouislider": "1.8.2", "nouislider": "^14.0.2", "node-sass": "^4.11.0", "replace-in-file": "^3.4.4", "rxjs": "5.4.3", "web-animations-js": "2.2.5", "zone.js": "0.8.17"}, "devDependencies": {"@angular/cli": "1.4.5", "@angular/compiler-cli": "^4.0.0", "@angular/language-service": "^4.0.0", "@types/jasmine": "2.5.45", "@types/node": "~6.0.60", "codelyzer": "~3.0.1", "jasmine-core": "~2.6.2", "jasmine-spec-reporter": "~4.1.0", "karma": "~1.7.0", "karma-chrome-launcher": "~2.1.1", "karma-cli": "~1.0.1", "karma-coverage-istanbul-reporter": "^1.2.1", "karma-jasmine": "~1.1.0", "karma-jasmine-html-reporter": "^0.2.2", "protractor": "~5.1.2", "ts-node": "~3.0.4", "tslint": "~5.3.2", "typescript": "~2.3.4", "webpack": "3.6.0", "webpack-bundle-analyzer": "^2.9.0", "copy-webpack-plugin": "4.3.1"}}