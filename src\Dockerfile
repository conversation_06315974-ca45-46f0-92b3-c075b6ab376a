FROM nginx:alpine

RUN apk add --no-cache bash

RUN mkdir /usr/share/nginx/html/web

COPY ./* /usr/share/nginx/html/web/

RUN rm -rf /usr/share/nginx/html/web/nginx.conf
RUN rm -rf /usr/share/nginx/html/web/Dockerfile
RUN rm -rf /usr/share/nginx/html/web/custom-configs.sh

COPY ./custom-configs.sh /docker-entrypoint.d/30-custom-configs.sh
COPY ./nginx.conf /etc/nginx/conf.d/default.conf

RUN dos2unix /docker-entrypoint.d/30-custom-configs.sh
RUN dos2unix /etc/nginx/conf.d/default.conf

RUN chmod a+x /docker-entrypoint.d/30-custom-configs.sh


