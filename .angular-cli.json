{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "project": {"name": "starter"}, "apps": [{"root": "src", "outDir": "dist", "assets": ["favicon.ico", "expiration.html", "custom-configs.js", "Dockerfile", "nginx.conf", "custom-configs.sh", {"glob": "**/*", "input": "./assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-custom-components/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-users-manager/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-parameter-manager/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-consent-manager/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-audit/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-privacy-data-treatment/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-subject-rights-management/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-data-breach/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-data-protection-impact-assessment/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-impact-assessment/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-privacy-data-treatment-in-progress/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-privacy-appointment-management/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-privacy-manager-configuration/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-training-and-education/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-access-manager/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-task-list/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-mail-configuration/src/assets/", "output": "./"}, {"glob": "**/*", "input": "../node_modules/eng-app-organization-manager/src/assets/", "output": "./"}], "index": "index.html", "main": "main.ts", "polyfills": "polyfills.ts", "test": "test.ts", "tsconfig": "tsconfig.app.json", "testTsconfig": "tsconfig.spec.json", "prefix": "app", "styles": ["styles.scss", "../node_modules/font-awesome/scss/font-awesome.scss"], "scripts": [], "environmentSource": "environments/environment.ts", "environments": {"dev": "environments/environment.ts", "prod": "environments/environment.prod.ts", "installer": "environments/environment.installer.ts"}}], "e2e": {"protractor": {"config": "./protractor.conf.js"}}, "lint": [{"project": "src/tsconfig.app.json"}, {"project": "src/tsconfig.spec.json"}, {"project": "e2e/tsconfig.e2e.json"}], "test": {"karma": {"config": "./karma.conf.js"}}, "defaults": {"styleExt": "css", "component": {}, "build": {"showCircularDependencies": false}}}