<!doctype html>
<html>
<head>
  <meta charset="utf-8">
  <title>EngApp External Laucher</title>
  <base href="/">

  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
</head>

<body>

	<p>Parametri per lancio EngApp</p>
	<textarea id="parametriLancio" style="width: 100%; height: 300px;">
	</textarea>
	
	<p>Base Url EngApp</p>
	<input id="urlEngApp" style="width: 100%;">
	
	<br><br>
	
	<button id="createUrlBtn">
	EncodeUrl
	</button>
	<button id="goBtn">
	Open
	</button>
	
	<textarea id="urlEncoded" style="width: 100%; height: 50px;">
	</textarea>
	<div id="urlLength"></div>
	
	<script>
		$(function(){
			
			var contextPath = location.href.substr(0,location.href.indexOf("/careservices"));
			var initialParams = {
				"REMOTE_DATASTORE_URL": contextPath+"/rest",
				"ENG_APP_USER":	{
					"username": "GSACCO",
					"credentials": "GS",
				    "profile" : "03|DIPCARDIO|MEDICO"
				},
				"ENG_APP_START_ACTIVITY":{
					"pageName": "icu.episodeOfCare.page",
					"mainObjectKey": "2823", /*Nosologico 2016010012 in CARDIOLOGIA, db : CLINIC_TEST_201105/)*/
					"activityName": "observations-list-activity"
				},
				"ENG_APP_AUTOSTARTED_MODULES":[
					"IcuBaseModule"
				],
				"FORCE_IGNORE_MODULES":[     
					"EngAppSandboxModule", 
				    "EngAppDashboardsModule"
				],
				"ENG_APP_SKIP_ACTIVITY_CONFIGURATION": true,
				"PAGE_MENU_MODE_CONFIGS": [
					/*{
						"activityName": "*",
				      	"pageMenuMode": "EXPANDED"
				    },*/{
				      	"activityName": "observations-list-activity",
				      	"pageMenuMode": "HIDDEN"
				    }
				]
			};
			
			var getParamsValue = function() {
				var jsonParams = $('#parametriLancio').val();
				var jsonParamsObject = JSON.parse(jsonParams);
				var reduced = JSON.stringify(jsonParamsObject);
				return reduced;
			};
			var getEncodedUrl = function() {
				return $('#urlEngApp').val()+"?ENGAPPCONFIGS="+encodeURIComponent(getParamsValue());
			};
			
			$('#parametriLancio').val(JSON.stringify(initialParams,null,2));

			$('#urlEngApp').val("http://localhost:4200/");
			
			$('#createUrlBtn').click(()=>{
				var url = getEncodedUrl();
				$('#urlEncoded').val(url);
				$('#urlLength').html("Length: "+url.length+" chars");
			});
			
			$('#goBtn').click(()=>{
				window.open(getEncodedUrl(),"engApp");
			});
		});
	</script>

</body>
</html>
