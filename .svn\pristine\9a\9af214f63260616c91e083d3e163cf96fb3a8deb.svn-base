import { NgModule, ModuleWithProviders } from '@angular/core';
import { BrowserModule  } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { environment } from '../environments/environment';

import {
    EngAppModule,
    EngAppSystemModule,
    EngAppViewportComponent,
    EngApplication,
    REMOTE_DATASTORE_URL,
    ENG_APP_NAME,
    ENG_APP_VERSION,
    DEFAULT_SEARCH_PAGE_SIZE,
    ENG_APP_SKIP_ACTIVITY_CONFIGURATION,
    ENG_APP_AUTOSTARTED_MODULES,
    ENG_APP_USER
} from 'eng-app';

import { EngAppCustomComponentsModule } from 'eng-app-custom-components';

export const AppRoutings: ModuleWithProviders = RouterModule.forRoot([
    {
        path: 'EngAppUsersManagerModuleLoader',
        loadChildren: 'app/lazy/eng-app-users-manager-loader.module#EngAppUsersManagerModuleLoader'
    }, {
        path: 'EngAppAuditModuleLoader',
        loadChildren: 'app/lazy/eng-app-audit-loader.module#EngAppAuditModuleLoader'
    }, {
        path: 'EngAppParameterManagerModule',
        loadChildren: 'app/lazy/eng-app-parameter-manager-loader.module#EngAppParameterManagerModuleLoader'
    }, {
        path: 'EngAppPrivacyDataTreatmentModuleLoader',
        loadChildren: 'app/lazy/eng-app-privacy-data-treatment-loader.module#EngAppPrivacyDataTreatmentModuleLoader'
    }, {
        path: 'EngAppDataProtectionImpactAssessmentModuleLoader',
        loadChildren: 'app/lazy/eng-app-data-protection-impact-assessment-loader.module#EngAppDataProtectionImpactAssessmentModuleLoader'
    }, {
        path: 'EngAppImpactAssessmentModuleLoader',
        loadChildren: 'app/lazy/eng-app-impact-assessment-loader.module#EngAppImpactAssessmentModuleLoader',
        data: {
            depends: [ 'EngAppPrivacyDataTreatmentModuleLoader' ]
        }
    }, {
        path: 'EngAppConsentManagerModuleLoader',
        loadChildren: 'app/lazy/eng-app-consent-manager-loader.module#EngAppConsentManagerModuleLoader'
    }, {
        path: 'EngAppDataBreachModuleLoader',
        loadChildren: 'app/lazy/eng-app-data-breach-loader.module#EngAppDataBreachModuleLoader',
          data: {
            depends: [ 'EngAppPrivacyDataTreatmentModuleLoader' ]
        }
    }, {
        path: 'EngAppSubjectRightsManagementModuleLoader',
        loadChildren: 'app/lazy/eng-app-subject-rights-management-loader.module#EngAppSubjectRightsManagementModuleLoader',
		data: {
			depends: [ 'EngAppPrivacyDataTreatmentModuleLoader' ]
		}
	},{
        path: 'EngAppPrivacyDataTreatmentInProgressModuleLoader',
        loadChildren: 'app/lazy/eng-app-privacy-data-treatment-in-progress-loader.module#EngAppPrivacyDataTreatmentInProgressModuleLoader'
    },{
        path: 'EngAppPrivacyManagerConfigurationModuleLoader',
        loadChildren: 'app/lazy/eng-app-privacy-manager-configuration-loader.module#EngAppPrivacyManagerConfigurationModuleLoader',
        data: {
            depends: [ 'EngAppPrivacyDataTreatmentModuleLoader' ]
        }
    },
    {
        path: 'EngAppTrainingAndEducationModuleLoader',
        loadChildren: 'app/lazy/eng-app-training-and-education-loader.module#EngAppTrainingAndEducationModuleLoader',
        data: {
          depends: [ 'EngAppPrivacyDataTreatmentModuleLoader' ]
        }
    },
    {
        path: 'EngAppAccessManagerModuleLoader',
        loadChildren: 'app/lazy/eng-app-access-manager-loader.module#EngAppAccessManagerModuleLoader'
    },
    {
        path: 'EngAppTaskListModuleLoader',
        loadChildren: 'app/lazy/eng-app-task-list-loader.module#EngAppTaskListModuleLoader'
    },  {
        path: 'EngAppMailConfigurationModuleLoader',
        loadChildren: 'app/lazy/eng-app-mail-configuration-loader.module#EngAppMailConfigurationModuleLoader'
    },
    {
        path: 'EngAppOrganizationManagerModuleLoader',
        loadChildren: 'app/lazy/eng-app-organization-manager-loader.module#EngAppOrganizationManagerModuleLoader'
    },
    {
        path: '**',
        redirectTo: '/'
    }
]);

@NgModule({
    declarations: [],
    imports: [
        RouterModule,
        BrowserModule,
        BrowserAnimationsModule,
        AppRoutings,
        EngAppModule,
        EngAppSystemModule,
        EngAppCustomComponentsModule
    ],
    providers: [],
    bootstrap: [EngAppViewportComponent]
})
export class AppModule {

    constructor(engApplication: EngApplication) {

        engApplication.setConfigKey(REMOTE_DATASTORE_URL, "../api");
        engApplication.setConfigKey(DEFAULT_SEARCH_PAGE_SIZE, 10);
        engApplication.setConfigKey(ENG_APP_NAME, environment.applicationName);
        engApplication.setConfigKey(ENG_APP_VERSION, 'development');
        engApplication.setConfigKey(ENG_APP_SKIP_ACTIVITY_CONFIGURATION, true);

        engApplication.setDefaultLang('it');

        engApplication.setConfigKey(ENG_APP_AUTOSTARTED_MODULES, [
            "**"
        ]);

        /*
        engApplication.setConfigKey(ENG_APP_USER,{
            username: "devuser",
            profile: "",
            credentials: "-**********",
            lang: "en"
        });*/
    }

}
